"use client"

import React from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { NavigationErrorBoundary } from "@/components/section-error-boundaries"

interface NavigationLink {
  href: string
  label: string
  ariaLabel: string
}

interface NavigationProps {
  /** Additional CSS classes for the navigation container */
  className?: string
  /** Custom navigation links (optional) */
  links?: NavigationLink[]
  /** Logo source path */
  logoSrc?: string
  /** Logo alt text */
  logoAlt?: string
  /** Show language selector */
  showLanguageSelector?: boolean
  /** Show get started button */
  showGetStartedButton?: boolean
  /** Get started button text */
  getStartedText?: string
  /** Get started button click handler */
  onGetStartedClick?: () => void
}

const defaultLinks: NavigationLink[] = [
  {
    href: "#hero",
    label: "Home",
    ariaLabel: "Go to home section",
  },
  {
    href: "#features",
    label: "Features",
    ariaLabel: "Go to features section",
  },
  {
    href: "#use-cases",
    label: "Solutions",
    ariaLabel: "Go to solutions section",
  },
  {
    href: "#pricing",
    label: "Pricing",
    ariaLabel: "Go to pricing section",
  },
  {
    href: "#docs",
    label: "Docs",
    ariaLabel: "Go to documentation",
  },
]

/**
 * Navigation Component
 *
 * A reusable navigation header with logo, navigation links, language selector,
 * and call-to-action button. Includes mobile responsiveness and accessibility features.
 *
 * @param props - Component props
 * @returns JSX.Element - The rendered navigation header
 */
function NavigationContent({
  className = "",
  links = defaultLinks,
  logoSrc = "/42logo-figma.svg",
  logoAlt = "42 logo",
  showLanguageSelector = true,
  showGetStartedButton = true,
  getStartedText = "Get Started",
  onGetStartedClick,
}: NavigationProps) {
  return (
    <nav className={`flex items-center justify-between px-8 h-16 bg-white/95 backdrop-blur-sm sticky top-0 z-50 ${className}`}>
      {/* Logo */}
      <div className="flex items-center space-x-3">
        <Image
          src={logoSrc}
          alt={logoAlt}
          width={42}
          height={42}
          className="w-[42px] h-[42px]"
        />
      </div>

      {/* Navigation Links */}
      <div
        className="hidden md:flex items-center space-x-10"
        role="navigation"
        aria-label="Main navigation"
      >
        {links.map((link) => (
          <a
            key={link.href}
            href={link.href}
            className="text-slate-600 hover:text-cyan-800 transition-colors font-medium"
            aria-label={link.ariaLabel}
          >
            {link.label}
          </a>
        ))}
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center space-x-4">
        {showLanguageSelector && (
          <select className="bg-transparent text-sm text-slate-600 font-medium">
            <option>English</option>
          </select>
        )}
        {showGetStartedButton && (
          <Button 
            className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-400 hover:to-emerald-500 text-white px-6 py-2 font-semibold transition-all duration-300 rounded-md cursor-pointer"
            onClick={onGetStartedClick}
          >
            {getStartedText}
          </Button>
        )}
      </div>
    </nav>
  )
}

/**
 * Navigation Component with Error Boundary
 *
 * Wraps the navigation content with the existing NavigationErrorBoundary
 * for graceful error handling.
 */
export function Navigation(props: NavigationProps) {
  return (
    <NavigationErrorBoundary>
      <NavigationContent {...props} />
    </NavigationErrorBoundary>
  )
}

export default Navigation
