"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import React, { useEffect, useRef, useState, useMemo } from "react"
import Image from "next/image"
import UseCases from "@/components/use-cases"

import { SpotlightProvider, useSpotlight, motion } from "@/hooks/spotlight"
import { TYPING_ANIMATION } from "@/lib/constants"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import {
  HeroErrorBoundary,
  SpotlightErrorBoundary,
  UseCasesErrorBoundary,
  NavigationErrorBoundary,
  TypingAnimationErrorBoundary,
  FeaturesErrorBoundary,
  CTAErrorBoundary,
} from "@/components/section-error-boundaries"
import { ErrorBoundaryTest } from "@/components/error-boundary-test"

/** Type alias for div element refs that may be null */
type DivMaybeNullRef = React.RefObject<HTMLDivElement | null>
/** Type alias for generic element refs that may be null */
type ElemMaybeNullRef = React.RefObject<HTMLElement | null>

/**
 * SpotlightWire Component
 *
 * Renders the visual spotlight element and connects it to the spotlight animation hook.
 * Creates a radial gradient effect that follows the mouse cursor.
 *
 * @param {Object} props - Component props
 * @param {DivMaybeNullRef} props.hostRef - Reference to the host container
 * @param {DivMaybeNullRef} props.spotRef - Reference to the spotlight element
 * @param {Array<ElemMaybeNullRef>} props.heroRefs - Array of element refs to highlight
 * @returns {JSX.Element} The rendered spotlight element
 */
function SpotlightWire(props: {
  hostRef: DivMaybeNullRef
  spotRef: DivMaybeNullRef
  heroRefs: Array<ElemMaybeNullRef>
}) {
  const { spotlightTransform } = useSpotlight(props)

  return (
    <motion.div
      ref={props.spotRef}
      className="absolute pointer-events-none w-60 h-60 rounded-full z-10"
      style={{
        transform: spotlightTransform,
        background: `radial-gradient(
          circle at center,
          rgba(255, 255, 255, 0.22) 0%,
          rgba(255, 255, 255, 0.14) 30%,
          rgba(255, 255, 255, 0.07) 58%,
          rgba(255, 255, 255, 0.02) 82%,
          rgba(255, 255, 255, 0) 100%
        )`,
        mixBlendMode: "screen",
        backdropFilter: "brightness(1.45) saturate(1.1) blur(1px)",
        WebkitBackdropFilter: "brightness(1.45) saturate(1.1) blur(1px)",
        maskImage: `radial-gradient(
          circle at center,
          rgba(0, 0, 0, 1) 0%,
          rgba(0, 0, 0, 0.85) 35%,
          rgba(0, 0, 0, 0.5) 60%,
          rgba(0, 0, 0, 0.15) 82%,
          rgba(0, 0, 0, 0) 100%
        )`,
        WebkitMaskImage: `radial-gradient(
          circle at center,
          rgba(0, 0, 0, 1) 0%,
          rgba(0, 0, 0, 0.85) 35%,
          rgba(0, 0, 0, 0.5) 60%,
          rgba(0, 0, 0, 0.15) 82%,
          rgba(0, 0, 0, 0) 100%
        )`,
        willChange: "transform",
      }}
    />
  )
}

/**
 * HomePage Component
 *
 * Main landing page for the Go42 AI Troubleshooting Agent application.
 * Features an interactive spotlight animation, typing animation terminal,
 * navigation, hero section, features showcase, use cases carousel, and CTA.
 *
 * Key Features:
 * - Interactive spotlight effect that follows mouse movement
 * - Animated typing terminal showing troubleshooting scenarios
 * - Responsive design with mobile-first approach
 * - Comprehensive error boundaries for graceful error handling
 * - Accessibility support with keyboard navigation and ARIA labels
 * - SEO optimized with proper meta tags and semantic HTML
 *
 * @returns {JSX.Element} The complete homepage with all sections
 */
export default function HomePage() {
  const mysteryLayerRef = useRef<HTMLDivElement>(null)
  const spotRef = useRef<HTMLDivElement>(null)
  const heroNumberRef = useRef<HTMLHeadingElement>(null)
  const heroTitleRef = useRef<HTMLParagraphElement>(null)
  const heroTaglineRef = useRef<HTMLParagraphElement>(null)
  const heroCtaRef = useRef<HTMLDivElement>(null)

  const terminalLines = useMemo(
    () => [
      "Analyzing deep packets...",
      "Locating problem position...",
      "Providing actionable evidence...",
      "Explaining possible causes...",
      "Suggesting optimal solutions...",
      "Ready to implement fix",
      "Estimated resolution time: 3 minutes",
    ],
    []
  )

  const [typedLines, setTypedLines] = useState<string[]>(() =>
    new Array(terminalLines.length).fill("")
  )
  const [activeLine, setActiveLine] = useState<number>(0)
  const [isTypingInitialized, setIsTypingInitialized] = useState(false)

  // Memoized empty array for resetting typed lines
  const emptyLines = useMemo(() => new Array(terminalLines.length).fill(""), [terminalLines.length])

  // Terminal-like typing effect for right column lines with looping and end pause + caret blink
  useEffect(() => {
    let cancelled = false
    const delay = (ms: number) => new Promise(r => setTimeout(r, ms))
    ;(async () => {
      // Set initialized state after a brief delay
      setTimeout(() => setIsTypingInitialized(true), 100)

      while (!cancelled) {
        // reset for a new cycle
        setTypedLines([...emptyLines])
        for (let i = 0; i < terminalLines.length; i++) {
          if (cancelled) return
          setActiveLine(i)
          const text = terminalLines[i]
          if (!text) continue // Skip if text is undefined
          for (let j = 0; j <= text.length; j++) {
            if (cancelled) return
            setTypedLines(prev => {
              const next = [...prev] // Use spread operator instead of slice for better performance
              next[i] = text.slice(0, j)
              return next
            })
            await delay(TYPING_ANIMATION.CHARACTER_DELAY)
          }
          await delay(TYPING_ANIMATION.LINE_DELAY)
        }
        // After finishing the last line, keep caret blinking on the last line for a few seconds
        setActiveLine(terminalLines.length - 1)
        await delay(TYPING_ANIMATION.CYCLE_END_DELAY)
        setActiveLine(-1)
      }
    })()

    return () => {
      cancelled = true
    }
  }, [terminalLines, emptyLines])

  // Removed leading loader animation per new design; keep caret-only typing

  return (
    <SpotlightProvider>
      <div className="min-h-screen bg-background">
        {/* Navigation */}
        <NavigationErrorBoundary>
          <nav className="flex items-center justify-between px-8 h-16 bg-white/95 backdrop-blur-sm sticky top-0 z-50">
            <div className="flex items-center space-x-3">
              <Image
                src="/42logo-figma.svg"
                alt="42 logo"
                width={42}
                height={42}
                className="w-[42px] h-[42px]"
              />
            </div>
            <div
              className="hidden md:flex items-center space-x-10"
              role="navigation"
              aria-label="Main navigation"
            >
              <a
                href="#hero"
                className="text-slate-600 hover:text-cyan-800 transition-colors font-medium"
                aria-label="Go to home section"
              >
                Home
              </a>
              <a
                href="#features"
                className="text-slate-600 hover:text-cyan-800 transition-colors font-medium"
                aria-label="Go to features section"
              >
                Features
              </a>
              <a
                href="#use-cases"
                className="text-slate-600 hover:text-cyan-800 transition-colors font-medium"
                aria-label="Go to solutions section"
              >
                Solutions
              </a>
              <a
                href="#pricing"
                className="text-slate-600 hover:text-cyan-800 transition-colors font-medium"
                aria-label="Go to pricing section"
              >
                Pricing
              </a>
              <a
                href="#docs"
                className="text-slate-600 hover:text-cyan-800 transition-colors font-medium"
                aria-label="Go to documentation"
              >
                Docs
              </a>
            </div>
            <div className="flex items-center space-x-4">
              <select className="bg-transparent text-sm text-slate-600 font-medium">
                <option>English</option>
              </select>
              <Button className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-400 hover:to-emerald-500 text-white px-6 py-2 font-semibold transition-all duration-300 rounded-md cursor-pointer">
                Get Started
              </Button>
            </div>
          </nav>
        </NavigationErrorBoundary>

        {/* Hero Section - Lovart-inspired dark artistic background */}
        <HeroErrorBoundary>
          <section
            id="hero"
            className="relative bg-gradient-to-br from-slate-900 via-cyan-900 to-slate-800 text-white overflow-hidden min-h-screen flex items-center"
          >
            {/* Artistic background elements */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-20 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-float"></div>
              <div
                className="absolute bottom-20 right-20 w-80 h-80 bg-emerald-500/10 rounded-full blur-3xl animate-float"
                style={{ animationDelay: "2s" }}
              ></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-cyan-500/5 to-emerald-500/5 rounded-full blur-3xl"></div>
            </div>

            {/* Network visualization overlay */}
            <div className="absolute inset-0 opacity-5">
              <svg className="w-full h-full" viewBox="0 0 1200 800">
                <defs>
                  <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                <circle cx="200" cy="150" r="4" fill="currentColor" className="animate-pulse" />
                <circle
                  cx="400"
                  cy="300"
                  r="4"
                  fill="currentColor"
                  className="animate-pulse"
                  style={{ animationDelay: "1s" }}
                />
                <circle
                  cx="800"
                  cy="200"
                  r="4"
                  fill="currentColor"
                  className="animate-pulse"
                  style={{ animationDelay: "2s" }}
                />
                <circle
                  cx="1000"
                  cy="400"
                  r="4"
                  fill="currentColor"
                  className="animate-pulse"
                  style={{ animationDelay: "0.5s" }}
                />
                <line
                  x1="200"
                  y1="150"
                  x2="400"
                  y2="300"
                  stroke="currentColor"
                  strokeWidth="1"
                  opacity="0.3"
                />
                <line
                  x1="400"
                  y1="300"
                  x2="800"
                  y2="200"
                  stroke="currentColor"
                  strokeWidth="1"
                  opacity="0.3"
                />
                <line
                  x1="800"
                  y1="200"
                  x2="1000"
                  y2="400"
                  stroke="currentColor"
                  strokeWidth="1"
                  opacity="0.3"
                />
              </svg>
            </div>

            <SpotlightErrorBoundary>
              <div ref={mysteryLayerRef} className="absolute inset-0 z-0 cursor-none mystery-layer">
                {/* Wire spotlight logic into hooks */}
                <SpotlightWire
                  hostRef={mysteryLayerRef}
                  spotRef={spotRef}
                  heroRefs={[heroNumberRef, heroTitleRef, heroTaglineRef, heroCtaRef]}
                />
                <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  PACKET LOSS
                </div>
                <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  DNS TIMEOUT
                </div>
                <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  BANDWIDTH THROTTLING
                </div>
                <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  CONNECTION REFUSED
                </div>
                <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  LATENCY SPIKE
                </div>
                <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  SSL HANDSHAKE FAILED
                </div>
                <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  ARP POISONING
                </div>
                <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  DHCP EXHAUSTION
                </div>
                <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  ROUTING LOOP
                </div>
                <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  FIREWALL BLOCKED
                </div>
                <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  PORT UNREACHABLE
                </div>
                <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
                  VLAN MISMATCH
                </div>
              </div>
            </SpotlightErrorBoundary>

            <div className="relative z-20 container mx-auto px-8 py-20" ref={heroCtaRef}>
              <div className="max-w-5xl mx-auto text-center">
                <h1
                  ref={heroNumberRef}
                  className="font-serif font-bold text-8xl md:text-[12rem] mb-8 tracking-tight"
                  style={{ textShadow: "0 4px 12px rgba(0,0,0,0.15)" }}
                >
                  <span className="bg-gradient-to-r from-white via-cyan-50 to-emerald-50 bg-clip-text text-transparent">
                    42
                  </span>
                </h1>

                <p
                  ref={heroTitleRef}
                  className="text-3xl md:text-5xl text-emerald-400 font-medium mb-10 tracking-wide font-space-grotesk"
                  style={{ textShadow: "0 2px 8px rgba(0,0,0,0.12)" }}
                >
                  Don&apos;t troubleshoot. Just know.
                </p>

                <p
                  ref={heroTaglineRef}
                  className="text-2xl md:text-3xl text-cyan-100/90 max-w-4xl mx-auto mb-16 leading-relaxed font-serif"
                  style={{ textShadow: "0 2px 6px rgba(0,0,0,0.1)" }}
                >
                  From raw packets to real answers.
                </p>

                <Button
                  size="lg"
                  className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-400 hover:to-emerald-500 text-white px-16 py-8 text-2xl font-semibold rounded-md transition-all duration-300 transform hover:scale-105 cursor-pointer"
                  style={{ boxShadow: "0 4px 16px rgba(0,0,0,0.15)" }}
                >
                  Find Your Answer In Minutes
                </Button>
              </div>
            </div>
          </section>
        </HeroErrorBoundary>

        {/* Tagline Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-8 text-center">
            <h2 className="font-serif font-bold text-5xl md:text-7xl text-cyan-800 mb-8 tracking-tight">
              AI Understands Your Network
            </h2>
            <p className="text-2xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-light">
              Every minute of downtime costs more than money. <br />
              42 helps you fix issues before they impact your business.
            </p>
          </div>
        </section>

        {/* Why 42 Section */}
        <section className="bg-white py-20">
          <div className="container mx-auto px-8">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-20 items-start content-stretch">
                <div>
                  <h2 className="font-serif font-bold text-5xl text-cyan-800 mb-8 tracking-tight">
                    Just give the packets.
                  </h2>
                  <p className="text-xl text-slate-600 leading-relaxed mb-6 font-light">
                    42 plans, explores, and creates like a real troubleshooting expert — calling the
                    right tools, mapping out solutions, and bringing your technical vision to life.
                  </p>

                  <div className="bg-gradient-to-br from-cyan-50 to-emerald-50 px-6 pt-4 pb-6 rounded-2xl italic">
                    <div className="flex items-center space-x-4 mb-4 pl-[1ch]">
                      <span className="text-slate-500 font-medium">
                        Describe Your Network Issue
                      </span>
                    </div>
                    <p className="text-slate-800 italic font-medium text-lg leading-snug mb-6 pl-[1ch]">
                      Intermittent connectivity drops during peak hours.
                    </p>
                    <div className="flex items-center justify-start pl-[1ch]">
                      <p className="text-slate-800 font-bold text-lg mt-1 py-1">
                        Diagnose Now {">>>"}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="relative">
                  <h2 className="font-serif font-bold text-5xl text-cyan-800 mb-8 tracking-tight text-left">
                    Let 42 handle the rest.
                  </h2>
                  <TypingAnimationErrorBoundary>
                    <div className="overflow-hidden">
                      {/* Rounded rectangle, auto height, gradient background consistent with left */}
                      <div className="relative rounded-2xl px-6 py-6 bg-gradient-to-br from-cyan-50 to-emerald-50">
                        <div className="space-y-3">
                          {!isTypingInitialized ? (
                            <div className="flex items-center space-x-2 text-slate-700 font-mono pl-[1ch]">
                              <LoadingSpinner size="sm" color="secondary" />
                              <span>Initializing diagnostic agent...</span>
                            </div>
                          ) : (
                            terminalLines.map((line, idx) => (
                              <div key={idx} className="text-slate-700 font-mono pl-[1ch]">
                                {idx === 5 ? (
                                  <span className="font-bold">{typedLines[idx]}</span>
                                ) : (
                                  <span>{typedLines[idx]}</span>
                                )}
                                {idx === 5 && typedLines[5] === line ? (
                                  <span className="ml-2 font-bold">{">>>"}</span>
                                ) : null}
                                {activeLine === idx ? <span className="typing-caret" /> : null}
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    </div>
                  </TypingAnimationErrorBoundary>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Use Cases Section */}
        <UseCasesErrorBoundary>
          <UseCases />
        </UseCasesErrorBoundary>

        {/* Video Demonstration Section */}
        <section className="bg-white py-32">
          <div className="container mx-auto px-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="font-serif font-bold text-3xl md:text-4xl text-cyan-800 mb-8 tracking-tight max-w-4xl mx-auto">
                  In one minute, watch Agent 42 turn chaos into clarity.
                </h2>
              </div>
              <div className="relative max-w-6xl mx-auto">
                <div className="relative bg-white overflow-hidden">
                  <div className="aspect-video">
                    <iframe
                      src="https://www.youtube.com/embed/SgmuplXU2iY"
                      title="42 Network Troubleshooting Demo"
                      className="w-full h-full"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    ></iframe>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <FeaturesErrorBoundary>
          <section
            id="features"
            className="py-20"
            style={{ backgroundColor: "rgb(238, 238, 238)" }}
          >
            <div className="container mx-auto px-8">
              <div className="max-w-7xl mx-auto">
                <div className="text-center mb-20">
                  <h2 className="font-serif font-bold text-5xl text-cyan-800 mb-6 tracking-tight">
                    Empower Your Team with AI Agent
                  </h2>
                  <p className="text-xl text-slate-600 max-w-3xl mx-auto font-light">
                    Experience the power of AI-driven troubleshooting with comprehensive analysis
                    and intelligent solutions
                  </p>
                </div>

                <div className="grid lg:grid-cols-3 gap-10">
                  <Card className="p-10 bg-gradient-to-br from-white to-cyan-50/50 hover:bg-gradient-to-br hover:from-cyan-50/30 hover:to-cyan-100/50 transition-all duration-300 group">
                    <div className="flex items-start gap-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                        <span className="text-white text-2xl">🔍</span>
                      </div>
                      <h3 className="font-serif font-bold text-2xl text-cyan-800 leading-tight">
                        Diagnostic Power
                      </h3>
                    </div>
                    <div className="space-y-4 text-slate-600">
                      <p className="font-medium">Autonomous packet parsing and deep analysis</p>
                      <p className="font-medium">
                        Instant problem detection and root-cause pinpointing
                      </p>
                      <p className="font-medium">
                        Evidence collection with clear, reliable reports
                      </p>
                    </div>
                  </Card>

                  <Card className="p-10 bg-gradient-to-br from-white to-emerald-50/50 hover:bg-gradient-to-br hover:from-emerald-50/30 hover:to-emerald-100/50 transition-all duration-300 group">
                    <div className="flex items-start gap-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-cyan-600 to-cyan-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                        <span className="text-white text-2xl">🧠</span>
                      </div>
                      <h3 className="font-serif font-bold text-2xl text-cyan-800 leading-tight">
                        Knowledge at Your Fingertips
                      </h3>
                    </div>
                    <div className="space-y-4 text-slate-600">
                      <p className="font-medium">
                        Comprehensive expertise in TCP/IP, networking, and system operations
                      </p>
                      <p className="font-medium">
                        Context-aware intelligence that understands complex environments
                      </p>
                      <p className="font-medium">
                        Continuously evolving knowledge base that learns from every case
                      </p>
                    </div>
                  </Card>

                  <Card className="p-10 bg-gradient-to-br from-white to-slate-50 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-slate-100/50 transition-all duration-300 group">
                    <div className="flex items-start gap-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-slate-600 to-slate-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                        <span className="text-white text-2xl">⚡</span>
                      </div>
                      <h3 className="font-serif font-bold text-2xl text-cyan-800 leading-tight">
                        Solutions that Work
                      </h3>
                    </div>
                    <div className="space-y-4 text-slate-600">
                      <p className="font-medium">Step-by-step verification methods you can trust</p>
                      <p className="font-medium">
                        Targeted, effective fixes tailored to each unique issue
                      </p>
                      <p className="font-medium">
                        Preventive recommendations to stop problems before they happen
                      </p>
                    </div>
                  </Card>
                </div>
              </div>
            </div>
          </section>
        </FeaturesErrorBoundary>

        {/* CTA Section */}
        <CTAErrorBoundary>
          <section className="bg-gradient-to-br from-slate-900 via-cyan-900 to-slate-800 text-white relative overflow-hidden py-32">
            <div className="container mx-auto px-8 text-center relative z-10">
              <div className="max-w-5xl mx-auto">
                <h2 className="font-serif font-bold text-5xl md:text-7xl mb-8 tracking-tight">
                  From confusion to clarity
                </h2>

                <p className="text-3xl text-emerald-400 mb-12 font-serif font-medium">
                  Cut troubleshooting time. Keep your revenue flowing.
                </p>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-400 hover:to-emerald-500 text-white px-12 py-6 text-xl font-semibold rounded-md transition-all duration-300 transform hover:scale-105 cursor-pointer"
                >
                  Start Diagnosing Now
                </Button>
              </div>
            </div>
          </section>
        </CTAErrorBoundary>

        {/* Footer */}
        <footer className="bg-slate-900 text-white py-16">
          <div className="container mx-auto px-8">
            <div className="text-center">
              <div className="text-slate-400 font-medium">
                © 2025 42, Inc. All rights reserved.
              </div>
            </div>
          </div>
        </footer>
      </div>

      {/* Development-only error boundary test component */}
      <ErrorBoundaryTest />
    </SpotlightProvider>
  )
}
